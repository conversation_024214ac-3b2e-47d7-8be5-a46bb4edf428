import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:pocket_trac/app/modules/location_picker/controllers/location_picker_controller.dart';
import 'package:pocket_trac/constants.dart';
import 'package:talker_flutter/talker_flutter.dart';

void main() {
  group('LocationPickerController Tests', () {
    late LocationPickerController controller;

    setUp(() {
      Get.testMode = true;
      controller = LocationPickerController(talker: Talker());
    });

    tearDown(() {
      Get.reset();
    });

    test('should initialize with default values', () {
      // Act
      controller.onInit();

      // Assert
      expect(controller.selectedLocation, isNotNull);
      expect(controller.currentZoom, equals(Constants.defaultZoomLevel));
      expect(controller.locationName, isEmpty);
    });

    test('should handle zoom in correctly', () {
      // Arrange
      controller.onInit();
      final initialZoom = controller.currentZoom;

      // Act
      controller.zoomIn();

      // Assert
      expect(controller.currentZoom, equals(initialZoom + 1));
    });

    test('should handle zoom out correctly', () {
      // Arrange
      controller.onInit();
      final initialZoom = controller.currentZoom;

      // Act
      controller.zoomOut();

      // Assert
      expect(controller.currentZoom, equals(initialZoom - 1));
    });
  });
}
