import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:pocket_trac/app/models/erp_category.dart';
import 'package:pocket_trac/app/models/erp_order.dart';
import 'package:pocket_trac/app/modules/order_detail/controllers/order_detail_controller.dart';
import 'package:pocket_trac/app/repositories/category_repository.dart';
import 'package:pocket_trac/app/repositories/order_repository.dart';

import 'order_detail_default_category_test.mocks.dart';

@GenerateMocks([CategoryRepository, OrderRepository])
void main() {
  group('OrderDetailController 默認類別測試', () {
    late OrderDetailController controller;
    late MockCategoryRepository mockCategoryRepository;
    late MockOrderRepository mockOrderRepository;

    setUp(() {
      Get.testMode = true;

      mockCategoryRepository = MockCategoryRepository();
      mockOrderRepository = MockOrderRepository();

      Get.put<CategoryRepository>(mockCategoryRepository);
      Get.put<OrderRepository>(mockOrderRepository);

      controller = OrderDetailController();
    });

    tearDown(() {
      Get.reset();
    });

    test('當有最新交易時，應該設置最新交易的類別作為默認值', () async {
      // 準備測試數據
      final testCategory = ErpCategory(
        id: 1,
        name: '餐飲',
        color: '#4CAF50',
        icon: 'restaurant',
      );

      final testOrder = ErpOrder(
        id: 1,
        type: 1, // 支出
        amount: 100.0,
        triggerAt: DateTime.now(),
      );
      testOrder.parent.target = testCategory;

      final categories = [testCategory];

      // 設置 mock 行為
      when(mockCategoryRepository.getAllAsync()).thenAnswer((_) async => categories);
      when(mockOrderRepository.getLatestOrderAsync()).thenAnswer((_) async => testOrder);

      // 執行測試 - 通過 onInit 觸發 _loadCategories
      controller.onInit();
      await Future.delayed(const Duration(milliseconds: 100)); // 等待異步操作完成

      // 驗證結果
      expect(controller.selectedCategory.value, equals(testCategory));
      expect(controller.selectedTransactionType.value, equals(TransactionType.expense));
    });

    test('當最新交易的類別不在當前分類列表中時，不應該設置默認類別', () async {
      // 準備測試數據
      final availableCategory = ErpCategory(
        id: 1,
        name: '餐飲',
        color: '#4CAF50',
        icon: 'restaurant',
      );

      final unavailableCategory = ErpCategory(
        id: 2,
        name: '交通',
        color: '#2196F3',
        icon: 'directions_car',
      );

      final testOrder = ErpOrder(
        id: 1,
        type: 1,
        amount: 100.0,
        triggerAt: DateTime.now(),
      );
      testOrder.parent.target = unavailableCategory;

      final categories = [availableCategory]; // 只包含可用的類別

      // 設置 mock 行為
      when(mockCategoryRepository.getAllAsync()).thenAnswer((_) async => categories);
      when(mockOrderRepository.getLatestOrderAsync()).thenAnswer((_) async => testOrder);

      // 執行測試
      controller.onInit();
      await Future.delayed(const Duration(milliseconds: 100));

      // 驗證結果
      expect(controller.selectedCategory.value, isNull);
    });

    test('當沒有最新交易時，不應該設置默認類別', () async {
      // 準備測試數據
      final testCategory = ErpCategory(
        id: 1,
        name: '餐飲',
        color: '#4CAF50',
        icon: 'restaurant',
      );

      final categories = [testCategory];

      // 設置 mock 行為
      when(mockCategoryRepository.getAllAsync()).thenAnswer((_) async => categories);
      when(mockOrderRepository.getLatestOrderAsync()).thenAnswer((_) async => null);

      // 執行測試
      controller.onInit();
      await Future.delayed(const Duration(milliseconds: 100));

      // 驗證結果
      expect(controller.selectedCategory.value, isNull);
    });

    test('當最新交易沒有關聯類別時，不應該設置默認類別', () async {
      // 準備測試數據
      final testCategory = ErpCategory(
        id: 1,
        name: '餐飲',
        color: '#4CAF50',
        icon: 'restaurant',
      );

      final testOrder = ErpOrder(
        id: 1,
        type: 1,
        amount: 100.0,
        triggerAt: DateTime.now(),
      );
      // 不設置 parent.target

      final categories = [testCategory];

      // 設置 mock 行為
      when(mockCategoryRepository.getAllAsync()).thenAnswer((_) async => categories);
      when(mockOrderRepository.getLatestOrderAsync()).thenAnswer((_) async => testOrder);

      // 執行測試
      controller.onInit();
      await Future.delayed(const Duration(milliseconds: 100));

      // 驗證結果
      expect(controller.selectedCategory.value, isNull);
    });

    test('應該根據最新交易的類型設置正確的交易類型', () async {
      // 測試收入類型
      final incomeCategory = ErpCategory(
        id: 1,
        name: '薪水',
        color: '#4CAF50',
        icon: 'attach_money',
      );
      final incomeOrder = ErpOrder(id: 1, type: 2, amount: 5000.0, triggerAt: DateTime.now());
      incomeOrder.parent.target = incomeCategory;

      when(mockCategoryRepository.getAllAsync()).thenAnswer((_) async => [incomeCategory]);
      when(mockOrderRepository.getLatestOrderAsync()).thenAnswer((_) async => incomeOrder);

      controller.onInit();
      await Future.delayed(const Duration(milliseconds: 100));

      expect(controller.selectedTransactionType.value, equals(TransactionType.income));
    });

    test('應該正確設置不計類型的交易', () async {
      final ignoreCategory = ErpCategory(
        id: 1,
        name: '轉帳',
        color: '#9E9E9E',
        icon: 'swap_horiz',
      );
      final ignoreOrder = ErpOrder(id: 1, type: 0, amount: 100.0, triggerAt: DateTime.now());
      ignoreOrder.parent.target = ignoreCategory;

      when(mockCategoryRepository.getAllAsync()).thenAnswer((_) async => [ignoreCategory]);
      when(mockOrderRepository.getLatestOrderAsync()).thenAnswer((_) async => ignoreOrder);

      controller.onInit();
      await Future.delayed(const Duration(milliseconds: 100));

      expect(controller.selectedTransactionType.value, equals(TransactionType.ignore));
    });
  });
}
