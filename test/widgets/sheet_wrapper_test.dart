import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';

import 'package:pocket_trac/app/widgets/sheet_wrapper.dart';

void main() {
  group('SheetWrapper 測試', () {
    setUp(() {
      Get.testMode = true;
    });

    tearDown(() {
      Get.reset();
    });

    testWidgets('當 showHeader 為 true 時，應該顯示標題欄', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          home: Scaffold(
            body: SheetWrapper(
              title: '測試標題',
              showHeader: true,
              child: const Text('測試內容'),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 查找標題文字
      expect(find.text('測試標題'), findsOneWidget);
      // 查找關閉按鈕
      expect(find.byIcon(Icons.close), findsOneWidget);
      // 查找內容
      expect(find.text('測試內容'), findsOneWidget);
    });

    testWidgets('當 showHeader 為 false 時，應該隱藏標題欄', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          home: Scaffold(
            body: SheetWrapper(
              title: '測試標題',
              showHeader: false,
              child: const Text('測試內容'),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 標題文字應該不存在
      expect(find.text('測試標題'), findsNothing);
      // 關閉按鈕應該不存在
      expect(find.byIcon(Icons.close), findsNothing);
      // 內容應該存在
      expect(find.text('測試內容'), findsOneWidget);
      // 拖拽指示器應該仍然存在
      expect(find.byType(Container), findsWidgets);
    });

    testWidgets('默認情況下 showHeader 應該為 true', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          home: Scaffold(
            body: SheetWrapper(
              title: '默認測試',
              child: const Text('測試內容'),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 默認應該顯示標題欄
      expect(find.text('默認測試'), findsOneWidget);
      expect(find.byIcon(Icons.close), findsOneWidget);
    });

    test('SheetWrapper 構造函數參數測試', () {
      const wrapper = SheetWrapper(
        title: '測試',
        showHeader: false,
        child: Text('內容'),
      );

      expect(wrapper.title, equals('測試'));
      expect(wrapper.showHeader, equals(false));
      expect(wrapper.child, isA<Text>());
    });
  });
}
