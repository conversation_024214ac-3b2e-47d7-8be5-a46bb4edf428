import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:pocket_trac/app/widgets/number_keypad.dart';

void main() {
  group('NumberKeypad Widget Tests', () {
    late List<String> pressedDigits;
    late int deleteCount;
    
    setUp(() {
      pressedDigits = [];
      deleteCount = 0;
    });

    Widget createTestWidget() {
      return MaterialApp(
        home: Scaffold(
          body: NumberKeypad(
            onDigitPressed: (digit) {
              pressedDigits.add(digit);
            },
            onDeletePressed: () {
              deleteCount++;
            },
          ),
        ),
      );
    }

    testWidgets('should display all number buttons', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      
      // 檢查所有數字按鈕是否顯示
      for (int i = 0; i <= 9; i++) {
        expect(find.text(i.toString()), findsOneWidget);
      }
      
      // 檢查小數點按鈕
      expect(find.text('.'), findsOneWidget);
      
      // 檢查刪除按鈕
      expect(find.byIcon(Icons.backspace), findsOneWidget);
    });

    testWidgets('should call onDigitPressed when number button is tapped', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      
      // 點擊數字 1
      await tester.tap(find.text('1'));
      await tester.pump();
      
      expect(pressedDigits, contains('1'));
      
      // 點擊數字 5
      await tester.tap(find.text('5'));
      await tester.pump();
      
      expect(pressedDigits, contains('5'));
      
      // 點擊小數點
      await tester.tap(find.text('.'));
      await tester.pump();
      
      expect(pressedDigits, contains('.'));
    });

    testWidgets('should call onDeletePressed when delete button is tapped', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      
      // 點擊刪除按鈕
      await tester.tap(find.byIcon(Icons.backspace));
      await tester.pump();
      
      expect(deleteCount, equals(1));
      
      // 再次點擊刪除按鈕
      await tester.tap(find.byIcon(Icons.backspace));
      await tester.pump();
      
      expect(deleteCount, equals(2));
    });

    testWidgets('should have correct layout with 3 columns', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      
      // 檢查是否有 GridView
      expect(find.byType(GridView), findsOneWidget);
      
      // 檢查 GridView 的配置
      final gridView = tester.widget<GridView>(find.byType(GridView));
      expect(gridView.shrinkWrap, isTrue);
      expect(gridView.physics, isA<NeverScrollableScrollPhysics>());
    });

    testWidgets('should work in dark theme', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.dark(),
          home: Scaffold(
            body: NumberKeypad(
              onDigitPressed: (digit) {
                pressedDigits.add(digit);
              },
              onDeletePressed: () {
                deleteCount++;
              },
            ),
          ),
        ),
      );
      
      // 檢查在深色主題下是否正常顯示
      expect(find.text('1'), findsOneWidget);
      expect(find.byIcon(Icons.backspace), findsOneWidget);
      
      // 測試功能是否正常
      await tester.tap(find.text('9'));
      await tester.pump();
      
      expect(pressedDigits, contains('9'));
    });

    testWidgets('should have proper button styling', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      
      // 檢查是否有 Material 和 InkWell 組件
      expect(find.byType(Material), findsWidgets);
      expect(find.byType(InkWell), findsWidgets);
      
      // 檢查是否有正確的容器裝飾
      expect(find.byType(Container), findsWidgets);
    });

    testWidgets('should handle multiple rapid taps correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      
      // 快速點擊多個按鈕
      await tester.tap(find.text('1'));
      await tester.tap(find.text('2'));
      await tester.tap(find.text('3'));
      await tester.tap(find.text('.'));
      await tester.tap(find.text('4'));
      await tester.pump();
      
      expect(pressedDigits, equals(['1', '2', '3', '.', '4']));
      
      // 快速點擊刪除按鈕
      await tester.tap(find.byIcon(Icons.backspace));
      await tester.tap(find.byIcon(Icons.backspace));
      await tester.pump();
      
      expect(deleteCount, equals(2));
    });
  });
}
