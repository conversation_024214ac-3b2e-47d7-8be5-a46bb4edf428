<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PocketTrac - 新增交易</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .phone-container {
            width: 375px;
            height: 812px;
            margin: 0 auto;
            background: #000;
            border-radius: 25px;
            padding: 10px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }
        .phone-screen {
            width: 100%;
            height: 100%;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .keypad-button {
            width: 100%;
            height: 50px;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            font-size: 18px;
            font-weight: 600;
            color: #374151;
            transition: all 0.2s;
        }
        .keypad-button:hover {
            background: #f3f4f6;
        }
        .keypad-button:active {
            background: #e5e7eb;
            transform: scale(0.95);
        }
        .keypad-button.delete {
            background: #fef2f2;
            color: #dc2626;
            border-color: #fecaca;
        }
        .keypad-button.delete:hover {
            background: #fee2e2;
        }
    </style>
</head>
<body class="bg-gray-100 p-4">
    <div class="phone-container">
        <div class="phone-screen">
            <!-- Form Content -->
            <div class="flex-1 px-4 py-4 overflow-y-auto" style="padding-bottom: 100px;">
                <!-- 交易類型選擇 -->
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-medium mb-2">交易類型</label>
                    <div class="flex bg-gray-100 rounded-lg p-1">
                        <button class="flex-1 py-2 rounded-md text-center font-medium bg-white text-blue-500 shadow-sm text-sm">
                            <i class="fas fa-arrow-up mr-1"></i>支出
                        </button>
                        <button class="flex-1 py-2 rounded-md text-center font-medium text-gray-600 text-sm">
                            <i class="fas fa-arrow-down mr-1"></i>收入
                        </button>
                        <button class="flex-1 py-2 rounded-md text-center font-medium text-gray-600 text-sm">
                            <i class="fas fa-minus mr-1"></i>不計
                        </button>
                    </div>
                </div>
                <!-- 金額輸入 -->
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-medium mb-2">金額</label>
                    <div class="relative">
                        <span class="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-500 text-xl">$</span>
                        <input type="number" placeholder="0" id="amountInput"
                               class="w-full bg-white border border-gray-200 rounded-xl px-12 py-4 text-2xl text-center font-bold focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>

                <!-- 數字鍵盤 -->
                <div class="mb-6">
                    <div class="bg-gray-50 rounded-xl p-4">
                        <div class="grid grid-cols-3 gap-3">
                            <!-- 第一行 -->
                            <button class="keypad-button" onclick="addDigit('1')">1</button>
                            <button class="keypad-button" onclick="addDigit('2')">2</button>
                            <button class="keypad-button" onclick="addDigit('3')">3</button>

                            <!-- 第二行 -->
                            <button class="keypad-button" onclick="addDigit('4')">4</button>
                            <button class="keypad-button" onclick="addDigit('5')">5</button>
                            <button class="keypad-button" onclick="addDigit('6')">6</button>

                            <!-- 第三行 -->
                            <button class="keypad-button" onclick="addDigit('7')">7</button>
                            <button class="keypad-button" onclick="addDigit('8')">8</button>
                            <button class="keypad-button" onclick="addDigit('9')">9</button>

                            <!-- 第四行 -->
                            <button class="keypad-button" onclick="addDigit('.')">.</button>
                            <button class="keypad-button" onclick="addDigit('0')">0</button>
                            <button class="keypad-button delete" onclick="deleteDigit()">
                                <i class="fas fa-backspace"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 消費類別 -->
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-medium mb-2">消費類別</label>
                    <div class="relative">
                        <select class="w-full bg-white border border-gray-200 rounded-xl px-4 py-3 pr-10 appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">請選擇類別</option>
                            <option value="food" selected>🍽️ 餐飲</option>
                            <option value="transport">🚗 交通</option>
                            <option value="shopping">🛍️ 購物</option>
                            <option value="entertainment">🎮 娛樂</option>
                            <option value="home">🏠 居家</option>
                            <option value="medical">❤️ 醫療</option>
                            <option value="education">📚 教育</option>
                            <option value="other">📝 其他</option>
                        </select>
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                            <i class="fas fa-chevron-down text-gray-400"></i>
                        </div>
                    </div>
                </div>

                <!-- 日期時間 -->
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-medium mb-2">日期時間</label>
                    <input type="datetime-local" value="2024-03-15T14:30"
                           class="w-full bg-white border border-gray-200 rounded-xl px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <!-- 備註 -->
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-medium mb-2">備註</label>
                    <textarea placeholder="添加備註..." 
                              class="w-full bg-white border border-gray-200 rounded-xl px-4 py-3 h-24 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                </div>

                <!-- 地點 -->
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-medium mb-2">地點 (選填)</label>
                    <button class="w-full bg-white border border-gray-200 rounded-xl px-4 py-3 text-left flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-map-marker-alt text-gray-400"></i>
                            <span class="text-gray-500">添加地點</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </button>
                </div>


            </div>

            <!-- Action Buttons -->
            <div class="absolute bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-4">
                <div class="flex space-x-3">
                    <button class="flex-1 bg-gray-100 text-gray-600 py-3 rounded-xl font-medium">
                        取消
                    </button>
                    <button class="flex-1 bg-blue-500 text-white py-3 rounded-xl font-medium">
                        儲存交易
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function addDigit(digit) {
            const input = document.getElementById('amountInput');
            const currentValue = input.value;

            // 如果是小數點，檢查是否已經有小數點
            if (digit === '.' && currentValue.includes('.')) {
                return;
            }

            // 如果當前值是 "0"，且輸入的不是小數點，則替換
            if (currentValue === '0' && digit !== '.') {
                input.value = digit;
            } else {
                input.value = currentValue + digit;
            }

            // 觸發 input 事件以便其他監聽器能夠響應
            input.dispatchEvent(new Event('input'));
        }

        function deleteDigit() {
            const input = document.getElementById('amountInput');
            const currentValue = input.value;

            if (currentValue.length > 0) {
                input.value = currentValue.slice(0, -1);

                // 如果刪除後為空，設置為 "0"
                if (input.value === '') {
                    input.value = '0';
                }
            }

            // 觸發 input 事件
            input.dispatchEvent(new Event('input'));
        }

        // 防止手動輸入時出現問題
        document.getElementById('amountInput').addEventListener('input', function(e) {
            let value = e.target.value;

            // 移除非數字和小數點的字符
            value = value.replace(/[^0-9.]/g, '');

            // 確保只有一個小數點
            const parts = value.split('.');
            if (parts.length > 2) {
                value = parts[0] + '.' + parts.slice(1).join('');
            }

            e.target.value = value;
        });
    </script>
</body>
</html>