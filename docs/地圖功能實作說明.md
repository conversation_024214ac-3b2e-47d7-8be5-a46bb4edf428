# 地圖功能實作說明

## 概述

本專案已成功實作了地圖選擇功能，允許使用者在訂單詳情頁面選擇地理位置。地圖使用台灣國土測繪中心的 WMTS 服務，並採用移動地圖的方式來選擇位置。

## 技術架構

### 使用的套件

- `flutter_map: ^8.1.1` - 主要地圖套件
- `latlong2: ^0.9.1` - 經緯度處理
- `flutter_map_location_marker: ^10.1.0` - 位置標記功能

### 地圖服務

- **地圖圖層**: 台灣國土測繪中心 WMTS 服務
- **URL**: `https://wmts.nlsc.gov.tw/wmts/EMAP/default/GoogleMapsCompatible/{z}/{y}/{x}`
- **縮放範圍**: 1-18 級

## 功能特色

### 1. 移動地圖選擇位置

- 使用者可以通過拖拽地圖來選擇位置
- 地圖中心有固定的紅色標記指示當前選擇的位置
- 即時顯示選擇位置的經緯度座標

### 2. 地圖控制

- **縮放控制**: 提供放大/縮小按鈕
- **定位按鈕**: 預留未來定位功能
- **位置資訊卡片**: 顯示當前選擇位置的詳細資訊

### 3. 與訂單系統整合

- 在訂單詳情頁面點擊地點選擇器開啟地圖
- 選擇位置後自動返回並更新訂單的地理位置資訊
- 地理位置資訊會儲存到 ErpOrder 模型的 latitude 和 longitude 欄位

## 檔案結構

```
lib/app/modules/location_picker/
├── controllers/
│   └── location_picker_controller.dart    # 地圖控制器
├── views/
│   └── location_picker_view.dart          # 地圖選擇頁面
└── bindings/
    └── location_picker_binding.dart       # 依賴注入綁定

lib/app/routes/
├── app_routes.dart                        # 路由定義
└── app_pages.dart                         # 路由配置

test/modules/
└── location_picker_test.dart              # 單元測試
```

## 主要類別說明

### LocationPickerController

負責地圖的業務邏輯處理：

- 地圖位置管理
- 縮放控制
- 位置選擇和確認
- 與父頁面的資料傳遞

### LocationPickerView

地圖選擇頁面的 UI 實作：

- 地圖顯示
- 控制按鈕
- 位置資訊卡片
- 底部操作按鈕

### OrderDetailController (更新)

新增了地理位置相關的功能：

- `latitude` 和 `longitude` 響應式變數
- `setLocationCoordinates()` 方法
- `clearLocation()` 方法
- 在 `saveTransaction()` 中儲存地理位置資訊

## 使用流程

1. 使用者在訂單詳情頁面點擊「地點 (選填)」
2. 開啟地圖選擇頁面，顯示預設位置（台北市）
3. 使用者拖拽地圖移動到想要的位置
4. 地圖中心的紅色標記指示當前選擇位置
5. 位置資訊卡片即時顯示經緯度座標
6. 點擊「確認選擇」按鈕返回訂單詳情頁面
7. 選擇的位置資訊會顯示在地點欄位中
8. 儲存訂單時，地理位置資訊會一併儲存到資料庫

## 測試覆蓋

已實作的單元測試包括：

- 控制器初始化測試
- 縮放功能測試
- 位置清除功能測試
- 邊界值測試（最大/最小縮放級別）

## 未來擴展

### 預留功能

1. **GPS 定位**: `moveToMyLocation()` 方法已預留
2. **地點搜尋**: `searchLocation()` 方法已預留
3. **地址反查**: 可整合地理編碼服務
4. **常用地點**: 可新增收藏地點功能

### 效能優化

1. 地圖圖層快取
2. 位置資訊本地儲存
3. 網路狀態檢測

## 注意事項

1. **網路連線**: 地圖需要網路連線才能載入圖層
2. **權限**: 未來如需 GPS 定位功能，需要位置權限
3. **效能**: 大量標記時需要考慮效能優化
4. **相容性**: 已測試在 Flutter 3.24.5+ 環境下正常運作

## 結論

地圖功能已成功整合到訂單管理系統中，提供了直觀的位置選擇體驗。使用移動地圖的方式比點擊地圖更加精確和用戶友好，符合現代移動應用的使用習慣。
