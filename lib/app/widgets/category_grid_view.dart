import 'package:flutter/material.dart';

import '../../colors.dart';
import '../models/erp_category.dart';
import 'category_grid_item.dart';

class CategoryGridView extends StatelessWidget {
  final List<ErpCategory> categories;
  final ErpCategory? selectedCategory;
  final ValueChanged<ErpCategory>? onCategorySelected;

  const CategoryGridView({
    super.key,
    required this.categories,
    this.selectedCategory,
    this.onCategorySelected,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    if (categories.isEmpty) {
      return Center(
        child: Text(
          '暫無分類',
          style: TextStyle(
            color:
                isDark ? ErpColors.darkTextSecondary : ErpColors.textSecondary,
          ),
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1.0,
      ),
      itemCount: categories.length,
      itemBuilder: (context, index) {
        final category = categories[index];
        return CategoryGridItem(
          category,
          isSelected: category == selectedCategory,
          onTap: () => onCategorySelected?.call(category),
        );
      },
    );
  }
}
