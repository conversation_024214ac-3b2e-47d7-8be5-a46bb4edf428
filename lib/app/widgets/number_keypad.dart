import 'package:flutter/material.dart';

import '../../colors.dart';

/// 數字鍵盤組件
class NumberKeypad extends StatelessWidget {
  /// 數字輸入回調
  final ValueChanged<String> onDigitPressed;

  /// 刪除數字回調
  final VoidCallback onDeletePressed;

  const NumberKeypad({
    super.key,
    required this.onDigitPressed,
    required this.onDeletePressed,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 3,
      crossAxisSpacing: 6,
      mainAxisSpacing: 6,
      childAspectRatio: 2.0,
      padding: EdgeInsets.zero,
      children: [
        // 第一行
        _buildKeypadButton('1', context),
        _buildKeypadButton('2', context),
        _buildKeypadButton('3', context),
        // 第二行
        _buildKeypadButton('4', context),
        _buildKeypadButton('5', context),
        _buildKeypadButton('6', context),
        // 第三行
        _buildKeypadButton('7', context),
        _buildKeypadButton('8', context),
        _buildKeypadButton('9', context),
        // 第四行
        _buildKeypadButton('.', context),
        _buildKeypadButton('0', context),
        _buildDeleteButton(context),
      ],
    );
  }

  /// 構建數字鍵盤按鈕
  Widget _buildKeypadButton(String digit, BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Material(
      color: isDark ? ErpColors.darkCardBackground : Colors.white,
      borderRadius: BorderRadius.circular(12),
      child: InkWell(
        onTap: () => onDigitPressed(digit),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: isDark ? ErpColors.darkBorder : const Color(0xFFE5E7EB),
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Center(
            child: Text(
              digit,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color:
                    isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 構建刪除按鈕
  Widget _buildDeleteButton(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Material(
      color: isDark ? const Color(0xFF2D1B1B) : const Color(0xFFFEF2F2),
      borderRadius: BorderRadius.circular(12),
      child: InkWell(
        onTap: onDeletePressed,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: isDark ? const Color(0xFF7F1D1D) : const Color(0xFFFECACA),
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Center(
            child: Icon(
              Icons.backspace,
              color: isDark ? const Color(0xFFEF4444) : const Color(0xFFDC2626),
              size: 18,
            ),
          ),
        ),
      ),
    );
  }
}