import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pocket_trac/extension.dart';

import '../../colors.dart';
import '../models/erp_order.dart';

class OrderItem extends StatelessWidget {
  final ErpOrder order;
  final VoidCallback? onTap;

  const OrderItem({
    super.key,
    required this.order,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: ErpColors.cardBackground,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: ErpColors.shadow,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        onTap: onTap,
        contentPadding: const EdgeInsets.all(16),
        leading: _buildTransactionIcon(),
        title: _buildTitle(),
        subtitle: _buildSubtitle(),
        trailing: _buildAmount(),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  Widget _buildTransactionIcon() {
    // TODO: Get category icon and color from order.parent
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: ErpColors.iconBackgroundBlue,
        borderRadius: BorderRadius.circular(24),
      ),
      child: const Icon(
        Icons.receipt,
        color: ErpColors.primary,
        size: 24,
      ),
    );
  }

  Widget _buildTitle() {
    return Text(
      order.note ?? 'transactions_no_data'.tr,
      style: const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: ErpColors.textPrimary,
      ),
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildSubtitle() {
    final triggerAt = order.triggerAt ?? DateTime.now();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '${triggerAt.formattedTime} • ${order.parent.target?.name ?? 'transactions_category'.tr}',
          style: const TextStyle(
            fontSize: 12,
            color: ErpColors.textSecondary,
          ),
        ),
        if (order.note != null && order.note!.length > 20) ...[
          const SizedBox(height: 2),
          Text(
            order.note!,
            style: const TextStyle(
              fontSize: 11,
              color: ErpColors.textHint,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ],
    );
  }

  Widget _buildAmount() {
    final isIncome = (order.type ?? 0) == 2;
    final color = isIncome ? ErpColors.income : ErpColors.expense;

    return Text(
      order.formattedAmountWithSign,
      style: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: color,
      ),
    );
  }
}
