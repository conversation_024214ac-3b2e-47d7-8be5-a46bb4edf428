import 'package:flutter/material.dart';
import 'package:pocket_trac/extension.dart';

import '../../colors.dart';
import '../models/erp_category.dart';

class CategoryGridItem extends StatelessWidget {
  final ErpCategory category;
  final bool isSelected;
  final VoidCallback? onTap;

  const CategoryGridItem(
    this.category, {
    super.key,
    this.isSelected = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: isSelected
              ? category.getColor().withValues(alpha: 0.1)
              : (isDark ? ErpColors.darkSurface : const Color(0xFFF9FAFB)),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected
                ? category.getColor()
                : (isDark ? ErpColors.darkBorder : const Color(0xFFE5E7EB)),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: _buildBodyChildren(isDark).toList(growable: false),
        ),
      ),
    );
  }

  Iterable<Widget> _buildBodyChildren(bool isDark) sync* {
    yield _buildCategoryIcon(isDark);
    yield const SizedBox(height: 8);
    yield _buildCategoryName(isDark);
  }

  Widget _buildCategoryIcon(bool isDark) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: category.getColor().withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Icon(
        category.getIcon(),
        size: 20,
        color: category.getColor(),
      ),
    );
  }

  Widget _buildCategoryName(bool isDark) {
    return Text(
      category.name ?? '',
      textAlign: TextAlign.center,
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: isSelected
            ? category.getColor()
            : (isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary),
      ),
    );
  }
}
