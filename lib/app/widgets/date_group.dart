import 'package:flutter/material.dart';

import '../../colors.dart';
import '../models/erp_order.dart';
import 'order_item.dart';

class DateGroup extends StatelessWidget {
  final String dateKey;
  final List<ErpOrder> orders;
  final Function(ErpOrder)? onOrderTap;

  const DateGroup({
    super.key,
    required this.dateKey,
    required this.orders,
    this.onOrderTap,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Text(
            dateKey,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: ErpColors.textPrimary,
            ),
          ),
        ),
        ...orders.map((order) => OrderItem(
              order: order,
              onTap: onOrderTap != null ? () => onOrderTap!(order) : null,
            )),
        const SizedBox(height: 16),
      ],
    );
  }
}
