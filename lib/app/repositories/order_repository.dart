import 'dart:async';

import 'package:objectid/objectid.dart';
import 'package:talker_flutter/talker_flutter.dart';

import '../models/erp_order.dart';
import '../providers/box_provider.dart';
import '../../objectbox.g.dart';
import '../providers/pref_provider.dart';

/// OrderFilter class for filtering orders with pagination support
class OrderFilter {
  final int? type;
  final double? minAmount;
  final double? maxAmount;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? noteSearch;
  final int? categoryId;
  final bool includeDeleted;
  final int limit;
  final int offset;
  final String? sortBy;
  final bool ascending;

  const OrderFilter({
    this.type,
    this.minAmount,
    this.maxAmount,
    this.startDate,
    this.endDate,
    this.noteSearch,
    this.categoryId,
    this.includeDeleted = false,
    this.limit = 20,
    this.offset = 0,
    this.sortBy,
    this.ascending = true,
  });

  OrderFilter copyWith({
    int? type,
    double? minAmount,
    double? maxAmount,
    DateTime? startDate,
    DateTime? endDate,
    String? noteSearch,
    int? categoryId,
    bool? includeDeleted,
    int? limit,
    int? offset,
    String? sortBy,
    bool? ascending,
  }) {
    return OrderFilter(
      type: type ?? this.type,
      minAmount: minAmount ?? this.minAmount,
      maxAmount: maxAmount ?? this.maxAmount,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      noteSearch: noteSearch ?? this.noteSearch,
      categoryId: categoryId ?? this.categoryId,
      includeDeleted: includeDeleted ?? this.includeDeleted,
      limit: limit ?? this.limit,
      offset: offset ?? this.offset,
      sortBy: sortBy ?? this.sortBy,
      ascending: ascending ?? this.ascending,
    );
  }
}

/// Repository for managing ErpOrder entities with ObjectBox
class OrderRepository {
  final BoxProvider boxProvider;

  /// Access to the Talker logger through BoxProvider
  Talker get talker => boxProvider.talker;
  
  /// Access to the ObjectBox store
  Store get store => boxProvider.store;
  
  /// Access to the ErpOrder box
  Box<ErpOrder> get box => store.box<ErpOrder>();

  OrderRepository(this.boxProvider);

  // ========== Basic CRUD Operations ==========

  /// Add a new order asynchronously
  Future<int> addAsync(ErpOrder order) async {
    try {
      final now = DateTime.now();
      order.createdAt = now;
      order.updatedAt = now;
      order.objectId ??= ObjectId().hexString;
      
      // final id = box.put(order); // Use sync put to ensure ID is set immediately
      final id = await box.putAsync(order);
      order.id = id; // Set the ID after putAsync to ensure it's available
      talker.debug('Order added successfully with ID: $id');
      return id;
    } catch (e, s) {
      talker.error('Failed to add order: $e', e, s);
      rethrow;
    }
  }

  /// Get order by ID asynchronously
  Future<ErpOrder?> getByIdAsync(int id) async {
    try {
      final order = await box.getAsync(id);
      if (order != null) {
        talker.debug('Order retrieved successfully with ID: $id');
      }
      return order;
    } catch (e, s) {
      talker.error('Failed to get order by ID $id: $e', e, s);
      rethrow;
    }
  }

  /// Get order by ObjectId asynchronously
  Future<ErpOrder?> getByObjectIdAsync(String objectId) async {
    try {
      final query = box.query(ErpOrder_.objectId.equals(objectId)).build();
      final order = await query.findFirstAsync();
      query.close();
      
      if (order != null) {
        talker.debug('Order retrieved successfully with ObjectId: $objectId');
      }
      return order;
    } catch (e, s) {
      talker.error('Failed to get order by ObjectId $objectId: $e', e, s);
      rethrow;
    }
  }

  /// Update an existing order asynchronously
  Future<void> updateAsync(ErpOrder order) async {
    try {
      order.updatedAt = DateTime.now();
      await box.putAsync(order);
      talker.debug('Order updated successfully with ID: ${order.id}');
    } catch (e, s) {
      talker.error('Failed to update order: $e', e, s);
      rethrow;
    }
  }

  /// Save order (add if new, update if existing) asynchronously
  Future<int> saveAsync(ErpOrder order) async {
    try {
      final now = DateTime.now();
      
      if (order.id == null || order.id == 0) {
        // New order
        order.createdAt = now;
        order.updatedAt = now;
        order.objectId = ObjectId().hexString;
      } else {
        // Existing order
        order.updatedAt = now;
      }
      
      final id = await box.putAsync(order);
      order.id = id; // Set the ID after putAsync to ensure it's available
      talker.debug('Order saved successfully with ID: $id');
      return id;
    } catch (e, s) {
      talker.error('Failed to save order: $e', e, s);
      rethrow;
    }
  }

  // ========== Query Operations ==========

  /// Get all orders asynchronously with optional includeDeleted flag
  Future<List<ErpOrder>> getAllAsync({bool includeDeleted = false}) async {
    try {
      Query<ErpOrder> query;

      if (!includeDeleted) {
        query = box.query(ErpOrder_.deletedAt.isNull()).build();
      } else {
        query = box.query().build();
      }

      final orders = await query.findAsync();
      query.close();

      talker.debug('Retrieved ${orders.length} orders (includeDeleted: $includeDeleted)');
      return orders;
    } catch (e, s) {
      talker.error('Failed to get all orders: $e', e, s);
      rethrow;
    }
  }

  /// Get the latest order asynchronously (most recent by triggerAt)
  Future<ErpOrder?> getLatestOrderAsync() async {
    try {
      final query = box.query(ErpOrder_.deletedAt.isNull())
          .order(ErpOrder_.triggerAt, flags: Order.descending)
          .build();

      query.limit = 1;
      final orders = await query.findAsync();
      query.close();

      final latestOrder = orders.isNotEmpty ? orders.first : null;
      if (latestOrder != null) {
        talker.debug('Retrieved latest order with ID: ${latestOrder.id}');
      } else {
        talker.debug('No orders found');
      }

      return latestOrder;
    } catch (e, s) {
      talker.error('Failed to get latest order: $e', e, s);
      rethrow;
    }
  }

  /// Get orders with filter support and pagination
  Future<List<ErpOrder>> getOrdersAsync(OrderFilter filter) async {
    try {
      // Build conditions list
      List<Condition<ErpOrder>> conditions = [];

      // Apply filters
      if (!filter.includeDeleted) {
        conditions.add(ErpOrder_.deletedAt.isNull());
      }

      if (filter.type != null) {
        conditions.add(ErpOrder_.type.equals(filter.type!));
      }

      if (filter.minAmount != null) {
        conditions.add(ErpOrder_.amount.greaterOrEqual(filter.minAmount!));
      }

      if (filter.maxAmount != null) {
        conditions.add(ErpOrder_.amount.lessOrEqual(filter.maxAmount!));
      }

      if (filter.startDate != null) {
        conditions.add(ErpOrder_.triggerAt.greaterOrEqual(filter.startDate!.millisecondsSinceEpoch));
      }

      if (filter.endDate != null) {
        conditions.add(ErpOrder_.triggerAt.lessOrEqual(filter.endDate!.millisecondsSinceEpoch));
      }

      if (filter.noteSearch != null && filter.noteSearch!.isNotEmpty) {
        conditions.add(ErpOrder_.note.contains(filter.noteSearch!));
      }

      if (filter.categoryId != null) {
        conditions.add(ErpOrder_.parent.equals(filter.categoryId!));
      }

      // Build query with combined conditions
      Query<ErpOrder> query;
      if (conditions.isEmpty) {
        query = box.query().build();
      } else if (conditions.length == 1) {
        query = box.query(conditions.first).build();
      } else {
        // Combine conditions with AND
        Condition<ErpOrder> combinedCondition = conditions.first;
        for (int i = 1; i < conditions.length; i++) {
          combinedCondition = combinedCondition.and(conditions[i]);
        }
        query = box.query(combinedCondition).build();
      }

      // Apply pagination
      query.offset = filter.offset;
      query.limit = filter.limit;

      final orders = await query.findAsync();
      query.close();

      talker.debug('Retrieved ${orders.length} orders with filter (limit: ${filter.limit}, offset: ${filter.offset})');
      return orders;
    } catch (e, s) {
      talker.error('Failed to get orders with filter: $e', e, s);
      rethrow;
    }
  }

  // ========== Delete Operations ==========

  /// Soft delete an order asynchronously
  Future<bool> softDeleteAsync(int id) async {
    try {
      final order = await getByIdAsync(id);
      if (order == null) {
        talker.warning('Order with ID $id not found for soft delete');
        return false;
      }
      
      order.deletedAt = DateTime.now();
      order.updatedAt = DateTime.now();
      await box.putAsync(order);
      
      talker.debug('Order soft deleted successfully with ID: $id');
      return true;
    } catch (e, s) {
      talker.error('Failed to soft delete order with ID $id: $e', e, s);
      rethrow;
    }
  }

  /// Restore a soft deleted order asynchronously
  Future<bool> restoreAsync(int id) async {
    try {
      final order = await getByIdAsync(id);
      if (order == null) {
        talker.warning('Order with ID $id not found for restore');
        return false;
      }

      order.deletedAt = null;
      order.updatedAt = DateTime.now();
      await box.putAsync(order);

      talker.debug('Order restored successfully with ID: $id');
      return true;
    } catch (e, s) {
      talker.error('Failed to restore order with ID $id: $e', e, s);
      rethrow;
    }
  }

  /// Hard delete an order asynchronously
  Future<bool> hardDeleteAsync(int id) async {
    try {
      final result = await box.removeAsync(id);
      if (result) {
        talker.debug('Order hard deleted successfully with ID: $id');
      } else {
        talker.warning('Order with ID $id not found for hard delete');
      }
      return result;
    } catch (e, s) {
      talker.error('Failed to hard delete order with ID $id: $e', e, s);
      rethrow;
    }
  }

  // ========== Count Operations ==========

  /// Count orders asynchronously with optional includeDeleted flag
  Future<int> countAsync({bool includeDeleted = false}) async {
    try {
      Query<ErpOrder> query;

      if (!includeDeleted) {
        query = box.query(ErpOrder_.deletedAt.isNull()).build();
      } else {
        query = box.query().build();
      }

      final count = query.count();
      query.close();

      talker.debug('Counted $count orders (includeDeleted: $includeDeleted)');
      return count;
    } catch (e, s) {
      talker.error('Failed to count orders: $e', e, s);
      rethrow;
    }
  }

  // ========== Batch Operations ==========

  /// Put many orders asynchronously
  Future<List<int>> putManyAsync(List<ErpOrder> orders) async {
    try {
      final now = DateTime.now();
      
      for (final order in orders) {
        if (order.id == null || order.id == 0) {
          // New order
          order.createdAt = now;
          order.updatedAt = now;
          order.objectId = ObjectId().hexString;
        } else {
          // Existing order
          order.updatedAt = now;
        }
      }
      
      final ids = await box.putManyAsync(orders);
      talker.debug('Put ${orders.length} orders successfully');
      return ids;
    } catch (e, s) {
      talker.error('Failed to put many orders: $e', e, s);
      rethrow;
    }
  }

  /// Delete all orders asynchronously
  Future<int> deleteAllAsync() async {
    try {
      final count = await box.removeAllAsync();
      talker.debug('Deleted all $count orders');
      return count;
    } catch (e, s) {
      talker.error('Failed to delete all orders: $e', e, s);
      rethrow;
    }
  }

  // ========== Stream Operations ==========

  /// Watch all orders stream
  Stream<List<ErpOrder>> watchAll() {
    try {
      final stream = box.query().watch(triggerImmediately: true).map((query) => query.find());
      talker.debug('Started watching all orders');
      return stream;
    } catch (e, s) {
      talker.error('Failed to watch all orders: $e', e, s);
      rethrow;
    }
  }

  /// Watch active (non-deleted) orders stream
  Stream<List<ErpOrder>> watchActive() {
    try {
      final stream = box.query(ErpOrder_.deletedAt.isNull()).watch(triggerImmediately: true).map((query) => query.find());
      talker.debug('Started watching active orders');
      return stream;
    } catch (e, s) {
      talker.error('Failed to watch active orders: $e', e, s);
      rethrow;
    }
  }

  /// Watch orders count stream
  Stream<int> watchCount({bool includeDeleted = false}) {
    try {
      Stream<Query<ErpOrder>> stream;

      if (!includeDeleted) {
        stream = box.query(ErpOrder_.deletedAt.isNull()).watch(triggerImmediately: true);
      } else {
        stream = box.query().watch(triggerImmediately: true);
      }

      final countStream = stream.map((query) => query.count());
      talker.debug('Started watching orders count (includeDeleted: $includeDeleted)');
      return countStream;
    } catch (e, s) {
      talker.error('Failed to watch orders count: $e', e, s);
      rethrow;
    }
  }

  /// Watch orders with filter
  Stream<List<ErpOrder>> watchOrdersWithFilter(OrderFilter filter) {
    try {
      // Build conditions list
      List<Condition<ErpOrder>> conditions = [];

      // Apply filters
      if (!filter.includeDeleted) {
        conditions.add(ErpOrder_.deletedAt.isNull());
      }

      if (filter.type != null) {
        conditions.add(ErpOrder_.type.equals(filter.type!));
      }

      if (filter.minAmount != null) {
        conditions.add(ErpOrder_.amount.greaterOrEqual(filter.minAmount!));
      }

      if (filter.maxAmount != null) {
        conditions.add(ErpOrder_.amount.lessOrEqual(filter.maxAmount!));
      }

      if (filter.startDate != null) {
        conditions.add(ErpOrder_.triggerAt.greaterOrEqual(filter.startDate!.millisecondsSinceEpoch));
      }

      if (filter.endDate != null) {
        conditions.add(ErpOrder_.triggerAt.lessOrEqual(filter.endDate!.millisecondsSinceEpoch));
      }

      if (filter.noteSearch != null && filter.noteSearch!.isNotEmpty) {
        conditions.add(ErpOrder_.note.contains(filter.noteSearch!));
      }

      if (filter.categoryId != null) {
        conditions.add(ErpOrder_.parent.equals(filter.categoryId!));
      }

      // Build query with combined conditions
      Stream<Query<ErpOrder>> stream;
      if (conditions.isEmpty) {
        stream = box.query().watch(triggerImmediately: true);
      } else if (conditions.length == 1) {
        stream = box.query(conditions.first).watch(triggerImmediately: true);
      } else {
        // Combine conditions with AND
        Condition<ErpOrder> combinedCondition = conditions.first;
        for (int i = 1; i < conditions.length; i++) {
          combinedCondition = combinedCondition.and(conditions[i]);
        }
        stream = box.query(combinedCondition).watch(triggerImmediately: true);
      }

      // Apply pagination and map to results
      final resultStream = stream.map((query) {
        query.offset = filter.offset;
        query.limit = filter.limit;
        return query.find();
      });

      talker.debug('Started watching orders with filter');
      return resultStream;
    } catch (e, s) {
      talker.error('Failed to watch orders with filter: $e', e, s);
      rethrow;
    }
  }

  /// Watch order by ID
  Stream<ErpOrder?> watchById(int id) {
    try {
      final stream = box.query(ErpOrder_.id.equals(id)).watch(triggerImmediately: true).map((query) => query.findFirst());
      talker.debug('Started watching order with ID: $id');
      return stream;
    } catch (e, s) {
      talker.error('Failed to watch order by ID $id: $e', e, s);
      rethrow;
    }
  }

  /// Watch order by ObjectId
  Stream<ErpOrder?> watchByObjectId(String objectId) {
    try {
      final stream = box.query(ErpOrder_.objectId.equals(objectId)).watch(triggerImmediately: true).map((query) => query.findFirst());
      talker.debug('Started watching order with ObjectId: $objectId');
      return stream;
    } catch (e, s) {
      talker.error('Failed to watch order by ObjectId $objectId: $e', e, s);
      rethrow;
    }
  }

  // ========== Note Operations ==========

  /// Get all distinct notes asynchronously (excluding null and empty notes)
  Future<List<String>> getDistinctNotesAsync({int limit = 50}) async {
    try {
      // Query all orders with non-null and non-empty notes
      final query = box.query(ErpOrder_.note.notNull().and(ErpOrder_.note.notEquals(''))).build();
      final orders = await query.findAsync();
      query.close();

      // Extract unique notes and sort by frequency/recency
      final noteMap = <String, DateTime>{};
      for (final order in orders) {
        if (order.note != null && order.note!.trim().isNotEmpty) {
          final note = order.note!.trim();
          final existingDate = noteMap[note];
          final orderDate = order.createdAt ?? DateTime.now();

          // Keep the most recent date for each note
          if (existingDate == null || orderDate.isAfter(existingDate)) {
            noteMap[note] = orderDate;
          }
        }
      }

      // Sort by most recent usage and limit results
      final sortedNotes = noteMap.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));

      final result = sortedNotes
          .take(limit)
          .map((entry) => entry.key)
          .toList();

      talker.debug('Retrieved ${result.length} distinct notes');
      return result;
    } catch (e, s) {
      talker.error('Failed to get distinct notes: $e', e, s);
      rethrow;
    }
  }

  /// Search notes asynchronously with query string
  Future<List<String>> searchNotesAsync(String query, {int limit = 20}) async {
    try {
      if (query.trim().isEmpty) {
        return await getDistinctNotesAsync(limit: limit);
      }

      // Query orders with notes containing the search query
      final searchQuery = box.query(
        ErpOrder_.note.notNull()
            .and(ErpOrder_.note.contains(query.trim(), caseSensitive: false))
            .and(ErpOrder_.deletedAt.isNull())
      ).build();

      final orders = await searchQuery.findAsync();
      searchQuery.close();

      // Extract unique notes that contain the query
      final noteSet = <String>{};
      for (final order in orders) {
        if (order.note != null && order.note!.trim().isNotEmpty) {
          final note = order.note!.trim();
          if (note.toLowerCase().contains(query.toLowerCase())) {
            noteSet.add(note);
          }
        }
      }

      // Convert to list and limit results
      final result = noteSet.take(limit).toList();

      talker.debug('Found ${result.length} notes matching query: "$query"');
      return result;
    } catch (e, s) {
      talker.error('Failed to search notes with query "$query": $e', e, s);
      rethrow;
    }
  }
}
