import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pocket_trac/app/routes/app_pages.dart';
import 'package:pocket_trac/extension.dart';

import '../../../../colors.dart';
import '../../../widgets/date_group.dart';
import '../controllers/orders_controller.dart';

class OrdersView extends GetView<OrdersController> {
  OrdersView({super.key});

  final ScrollController _scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    // Add scroll listener for pagination
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 200) {
        controller.onEndScroll();
      }
    });
    controller.talker.debug('OrdersView build');

    return Scaffold(
      body: controller.obx(
        (state) => _buildContent(context),
        onLoading: const Center(
          child: CircularProgressIndicator(),
        ),
        onError: (error) => Center(
          child: Text(error.toString()),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showOrderDetail,
        backgroundColor: ErpColors.primary,
        foregroundColor: Colors.white,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return CustomScrollView(
      controller: _scrollController,
      slivers: [
        _buildFloatingHeader(context),
        Obx(() => _buildTransactionListSliver(context)),
      ],
    );
  }

  Widget _buildFloatingHeader(BuildContext context) {
    return SliverAppBar(
      expandedHeight: 200,
      floating: true,
      pinned: false, // 改為 false 避免半透明遮擋
      snap: true, // 改為 true 提供更好的滾動體驗
      elevation: 0,
      backgroundColor: Colors.transparent,
      surfaceTintColor: Colors.transparent, // 確保沒有表面色調
      shadowColor: Colors.transparent, // 確保沒有陰影
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                ErpColors.gradientStart,
                ErpColors.gradientEnd,
              ],
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Column(
                children: [
                  _buildSearchBar(context),
                  const SizedBox(height: 16),
                  _buildFilterButtons(context),
                  const SizedBox(height: 16),
                  _buildStatistics(context),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTransactionListSliver(BuildContext context) {
    final groupedOrders = controller.groupedOrders;

    if (groupedOrders.isEmpty) {
      return SliverFillRemaining(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.receipt_long,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                'transactions_no_data'.tr,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      );
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          if (index == groupedOrders.length) {
            // Loading indicator for pagination
            return Obx(() {
              return controller.isLoadingMore.value
                  ? const Center(
                      child: Padding(
                        padding: EdgeInsets.all(16),
                        child: CircularProgressIndicator(),
                      ),
                    )
                  : const SizedBox.shrink();
            });
          }

          final dateKey = groupedOrders.keys.elementAt(index);
          final orders = groupedOrders[dateKey]!;

          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: DateGroup(
              dateKey: dateKey,
              orders: orders,
              onOrderTap: (order) {
                // TODO: Navigate to order detail
              },
            ),
          );
        },
        childCount:
            groupedOrders.length + (controller.hasMoreData.value ? 1 : 0),
      ),
    );
  }

  Widget _buildSearchBar(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: ErpColors.overlayMedium,
        borderRadius: BorderRadius.circular(12),
      ),
      child: TextField(
        onChanged: controller.searchOrders,
        style: const TextStyle(color: ErpColors.textWhite),
        decoration: InputDecoration(
          hintText: 'transactions_search'.tr,
          hintStyle: const TextStyle(color: ErpColors.overlayStrong),
          prefixIcon: const Icon(
            Icons.search,
            color: ErpColors.overlayStrong,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
      ),
    );
  }

  Widget _buildFilterButtons(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: _buildFilterButton(
            icon: Icons.calendar_today,
            label: 'transactions_date'.tr,
            onTap: () {
              // TODO: Show date filter
            },
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildFilterButton(
            icon: Icons.category,
            label: 'transactions_category'.tr,
            onTap: () {
              // TODO: Show category filter
            },
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildFilterButton(
            icon: Icons.filter_list,
            label: 'transactions_filter'.tr,
            onTap: () {
              // TODO: Show type filter
            },
          ),
        ),
      ],
    );
  }

  Widget _buildFilterButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        decoration: BoxDecoration(
          color: ErpColors.overlayMedium,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: ErpColors.textWhite,
              size: 16,
            ),
            const SizedBox(width: 4),
            Flexible(
              child: Text(
                label,
                style: const TextStyle(
                  color: ErpColors.textWhite,
                  fontSize: 12,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatistics(BuildContext context) {
    return Obx(() {
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: ErpColors.overlayLight,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                children: [
                  Text(
                    'transactions_income'.tr,
                    style: const TextStyle(
                      color: ErpColors.overlayStrong,
                      fontSize: 12,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '+${controller.totalIncome.value.formattedAmount}',
                    style: const TextStyle(
                      color: ErpColors.incomeLight,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: Column(
                children: [
                  Text(
                    'transactions_expense'.tr,
                    style: const TextStyle(
                      color: ErpColors.overlayStrong,
                      fontSize: 12,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '-${controller.totalExpense.value.formattedAmount}',
                    style: const TextStyle(
                      color: ErpColors.expenseLight,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    });
  }

  Future<void> _showOrderDetail() async {
    await Get.toNamed(Routes.ORDER_DETAIL);
  }
}
