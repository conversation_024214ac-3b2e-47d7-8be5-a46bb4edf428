import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:omni_datetime_picker/omni_datetime_picker.dart';
import 'package:pocket_trac/app/widgets/category_grid_view.dart';
import 'package:pocket_trac/app/widgets/location_view.dart';
import 'package:pocket_trac/app/widgets/number_keypad.dart';
import 'package:pocket_trac/app/widgets/sheet_wrapper.dart';
import 'package:pocket_trac/app/widgets/transaction_type_selector.dart';
import 'package:pocket_trac/constants.dart';
import 'package:pocket_trac/keys.dart';

import '../../../../colors.dart';
import '../../../../extension.dart';
import '../../../routes/app_pages.dart';
import '../../location_picker/widgets/bottom_actions_widget.dart';
import '../controllers/order_detail_controller.dart';

class OrderDetailView extends GetView<OrderDetailController> {
  const OrderDetailView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    Iterable<Widget> getChildren() sync* {
      yield Expanded(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: _buildBody(context).toList(growable: false),
          ),
        ),
      );
      yield _buildBottomActions(context);
    }

    return Scaffold(
      backgroundColor: isDark ? ErpColors.darkBackground : ErpColors.background,
      appBar: AppBar(
        title: const Text('交易詳情'),
        backgroundColor: isDark ? ErpColors.darkCardBackground : Colors.white,
        foregroundColor:
            isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
        elevation: 0,
      ),
      body: Column(
        children: getChildren().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _buildBody(BuildContext context) sync* {
    yield _buildAmountSection(context);
    yield const SizedBox(height: 8);
    yield NumberKeypad(
      onDigitPressed: controller.addDigit,
      onDeletePressed: controller.deleteDigit,
    );
    yield const SizedBox(height: 16);
    yield _buildNoteInput(context);
    yield const SizedBox(height: 16);
    yield _buildCategoryAndDateTimeRow(context);
    yield const SizedBox(height: 16);
    yield _buildTransactionTypeSelector(context);
    yield const SizedBox(height: 16);
    yield _buildLocationSelector(context);
  }

  // 交易類型選擇器
  Widget _buildTransactionTypeSelector(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    Iterable<Widget> getChildren() sync* {
      yield Text(
        '交易類型',
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
        ),
      );
      yield const SizedBox(height: 8);
      yield Obx(() {
        return TransactionTypeSelector(
          transactionType: controller.draft.transactionType,
          onChanged: (value) {
            controller.draft.transactionType = value;
            controller.refreshDraft();
          },
        );
      });
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: getChildren().toList(growable: false),
    );
  }

  // 金額輸入區域
  Widget _buildAmountSection(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    Iterable<Widget> getChildren() sync* {
      yield Text(
        '金額',
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
        ),
      );
      yield const SizedBox(height: 8);
      yield Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        decoration: BoxDecoration(
          color: isDark ? ErpColors.darkCardBackground : Colors.white,
          border: Border.all(
            color: isDark ? ErpColors.darkBorder : const Color(0xFFE5E7EB),
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Text(
              '\$',
              style: TextStyle(
                fontSize: 24,
                color: isDark
                    ? ErpColors.darkTextSecondary
                    : ErpColors.textSecondary,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Obx(() => Text(
                    controller.amount.value,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: isDark
                          ? ErpColors.darkTextPrimary
                          : ErpColors.textPrimary,
                    ),
                  )),
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: getChildren().toList(growable: false),
    );
  }

  // 分類和日期時間選擇器行
  Widget _buildCategoryAndDateTimeRow(BuildContext context) {
    return Row(
      children: [
        Expanded(child: _buildCategorySelector(context)),
        const SizedBox(width: 12),
        Expanded(child: _buildDateTimeSelector(context)),
      ],
    );
  }

  // 分類選擇器
  Widget _buildCategorySelector(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '消費類別',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Obx(() => GestureDetector(
              onTap: () => _showCategoryPicker(context),
              child: Container(
                width: double.infinity,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: isDark ? ErpColors.darkCardBackground : Colors.white,
                  border: Border.all(
                    color:
                        isDark ? ErpColors.darkBorder : const Color(0xFFE5E7EB),
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    if (controller.selectedCategory.value != null) ...[
                      Container(
                        width: 20,
                        height: 20,
                        decoration: BoxDecoration(
                          color: controller.selectedCategory.value!
                              .getColor()
                              .withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Icon(
                          controller.selectedCategory.value!.getIcon(),
                          size: 12,
                          color: controller.selectedCategory.value!.getColor(),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          controller.selectedCategory.value!.name ?? '',
                          style: TextStyle(
                            fontSize: 14,
                            color: isDark
                                ? ErpColors.darkTextPrimary
                                : ErpColors.textPrimary,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ] else ...[
                      Expanded(
                        child: Text(
                          '請選擇類別',
                          style: TextStyle(
                            fontSize: 14,
                            color: isDark
                                ? ErpColors.darkTextSecondary
                                : ErpColors.textSecondary,
                          ),
                        ),
                      ),
                    ],
                    Icon(
                      Icons.keyboard_arrow_down,
                      color: isDark
                          ? ErpColors.darkTextSecondary
                          : ErpColors.textSecondary,
                      size: 16,
                    ),
                  ],
                ),
              ),
            )),
      ],
    );
  }

  // 日期時間選擇器
  Widget _buildDateTimeSelector(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    Iterable<Widget> getChildren() sync* {
      yield Text(
        '日期時間',
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
        ),
      );
      yield const SizedBox(height: 8);
      yield GestureDetector(
        onTap: () => _showDateTimePicker(context),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: isDark ? ErpColors.darkCardBackground : Colors.white,
            border: Border.all(
              color: isDark ? ErpColors.darkBorder : const Color(0xFFE5E7EB),
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Obx(() {
            return Text(
              DateFormat('MM-dd HH:mm')
                  .format(controller.draft.triggerAt ?? DateTime.now()),
              style: TextStyle(
                fontSize: 14,
                color:
                    isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
              ),
            );
          }),
        ),
      );
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: getChildren().toList(growable: false),
    );
  }

  // 備註輸入
  Widget _buildNoteInput(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '備註',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        CompositedTransformTarget(
          link: controller.layerLink,
          child: Container(
            decoration: BoxDecoration(
              color: isDark ? ErpColors.darkCardBackground : Colors.white,
              border: Border.all(
                color: isDark ? ErpColors.darkBorder : const Color(0xFFE5E7EB),
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: TextField(
              controller: controller.noteController,
              onChanged: controller.setNote,
              onTap: () {
                // 當點擊輸入框時，如果有內容則顯示建議
                if (controller.noteController.text.trim().isNotEmpty) {
                  controller
                      .searchNoteSuggestions(controller.noteController.text);
                }
              },
              maxLines: 1,
              decoration: InputDecoration(
                hintText: '添加備註...',
                hintStyle: TextStyle(
                  color: isDark
                      ? ErpColors.darkTextSecondary
                      : ErpColors.textSecondary,
                ),
                border: InputBorder.none,
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              style: TextStyle(
                color:
                    isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
              ),
            ),
          ),
        ),
        // 自動完成建議列表 - 使用 Overlay
        Obx(() {
          if (controller.showNoteSuggestions.value &&
              controller.noteSuggestions.isNotEmpty) {
            // 確保 overlay 在下一幀顯示
            WidgetsBinding.instance.addPostFrameCallback((_) {
              controller.showOverlay(context);
            });
          } else {
            controller.hideOverlay();
          }
          return const SizedBox.shrink();
        }),
      ],
    );
  }

  // 地點選擇器
  Widget _buildLocationSelector(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    Iterable<Widget> getChildren() sync* {
      yield Text(
        '地點',
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
        ),
      );
      yield const SizedBox(height: 8);
      yield LocationView(
        location: controller.draft.latLng,
        onTap: () => _showLocationPicker(context),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: getChildren().toList(growable: false),
    );
  }

  // 底部操作按鈕
  Widget _buildBottomActions(BuildContext context) {
    return BottomActionsWidget(
      onCancel: () => Get.back(),
      onConfirm: controller.saveTransaction,
    );
  }

  // 顯示日期時間選擇器
  Future<void> _showDateTimePicker(BuildContext context) async {
    final DateTime? pickedDateTime = await showOmniDateTimePicker(
      context: context,
      initialDate: controller.draft.triggerAt,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
      is24HourMode: true,
      isShowSeconds: false,
      minutesInterval: 1,
      secondsInterval: 1,
      borderRadius: const BorderRadius.all(Radius.circular(16)),
      constraints: const BoxConstraints(
        maxWidth: 350,
        maxHeight: 650,
      ),
      transitionBuilder: (context, anim1, anim2, child) {
        return FadeTransition(
          opacity: anim1.drive(
            Tween(
              begin: 0,
              end: 1,
            ),
          ),
          child: child,
        );
      },
      transitionDuration: const Duration(milliseconds: 200),
      barrierDismissible: true,
      selectableDayPredicate: (dateTime) {
        // 可以在這裡添加日期限制邏輯
        return true;
      },
    );

    if (pickedDateTime != null) {
      controller.draft.triggerAt = pickedDateTime;
      controller.refreshDraft();
    }
  }

  // 顯示分類選擇器彈窗
  Future<void> _showCategoryPicker(BuildContext context) async {
    await SheetWrapper(
      showHeader: true,
      title: '選擇類別',
      child: Obx(() {
        return CategoryGridView(
          categories: controller.categories,
          selectedCategory: controller.selectedCategory.value,
          onCategorySelected: (category) {
            controller.setCategory(category);
            Navigator.of(context).pop();
          },
        );
      }),
    ).sheet(
      isDismissible: true,
      enableDrag: true,
      isScrollControlled: true,
      ignoreSafeArea: true,
    );
  }

  // 顯示地點選擇器
  Future<void> _showLocationPicker(BuildContext context) async {
    final draft = controller.draft;
    final result = await Get.toNamed(Routes.LOCATION_PICKER, parameters: {
      Keys.latitude: '${draft.latitude ?? Constants.taipeiLatitude}',
      Keys.longitude: '${draft.longitude ?? Constants.taipeiLongitude}',
    });

    if (result != null && result is Map<String, dynamic>) {
      final latitude = result[Keys.latitude] as double?;
      final longitude = result[Keys.longitude] as double?;

      controller.draft.latitude = latitude;
      controller.draft.longitude = longitude;
      controller.refreshDraft();
    }
  }
}
