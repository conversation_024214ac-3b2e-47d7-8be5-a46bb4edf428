import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:get/get.dart';
import 'package:latlong2/latlong.dart';
import 'package:pocket_trac/constants.dart';
import 'package:pocket_trac/keys.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:talker_flutter/talker_flutter.dart';

class LocationPickerController extends GetxController with StateMixin<String> {
  // 台灣預設中心點（台北市）
  static const LatLng defaultCenter =
      LatLng(Constants.taipeiLatitude, Constants.taipeiLongitude);

  final _disposable = Completer();
  final Talker talker;
  // 地圖控制器
  final MapController mapController = MapController();

  // 響應式變數
  final _selectedLocation = defaultCenter.obs;
  LatLng get selectedLocation => _selectedLocation.value;

  final _currentZoom = Constants.defaultZoomLevel.obs;
  double get currentZoom => _currentZoom.value;

  final _locationName = ''.obs;
  String get locationName => _locationName.value;

  LocationPickerController({
    required this.talker,
  });

  @override
  void onInit() {
    super.onInit();

    _selectedLocation.stream.takeUntil(_disposable.future).listen((value) {
      _locationName.value =
          '${value.latitude.toStringAsFixed(6)}, ${value.longitude.toStringAsFixed(6)}';
    });

    // 如果有傳入初始位置，使用它；否則使用預設位置
    final parameters = Get.parameters;
    if (parameters.isNotEmpty) {
      final lat = double.tryParse(parameters[Keys.latitude] ?? '') ?? 0.0;
      final lng = double.tryParse(parameters[Keys.longitude] ?? '') ?? 0.0;
      if (lat != 0.0 && lng != 0.0) {
        _selectedLocation.value = LatLng(lat, lng);
      }
    }
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh().then((_) {
      // 移動地圖到選定位置
      WidgetsBinding.instance.addPostFrameCallback((_) {
        mapController.move(selectedLocation, currentZoom);
      });
    });
  }

  @override
  void onClose() {
    _disposable.complete();
    mapController.dispose();
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      change(null, status: RxStatus.loading());
      // 模擬載入時間
      await Future.delayed(const Duration(milliseconds: 10));
      change('', status: RxStatus.success());
    } catch (e, s) {
      talker.error('Failed to refresh: $e', e, s);
      change(null, status: RxStatus.error('載入地圖失敗: $e'));
    }
  }

  // 處理地圖移動事件
  void onMapPositionChanged(MapCamera camera) {
    _selectedLocation.value = camera.center;
  }

  // 確認選擇位置
  void confirmLocation() {
    Get.back(result: {
      Keys.latitude: selectedLocation.latitude,
      Keys.longitude: selectedLocation.longitude,
    });
  }

  void cancel() {
    Get.back();
  }

  // 縮放地圖
  void zoomIn() {
    final newZoom =
        (currentZoom + 1).clamp(Constants.minZoomLevel, Constants.maxZoomLevel);
    _currentZoom.value = newZoom;
    try {
      mapController.move(selectedLocation, newZoom);
    } catch (e) {
      // MapController 尚未初始化，忽略錯誤
    }
  }

  void zoomOut() {
    final newZoom =
        (currentZoom - 1).clamp(Constants.minZoomLevel, Constants.maxZoomLevel);
    _currentZoom.value = newZoom;
    try {
      mapController.move(selectedLocation, newZoom);
    } catch (e) {
      // MapController 尚未初始化，忽略錯誤
    }
  }

  // 移動到我的位置（預留功能）
  void moveToMyLocation() {
    // TODO: 實作定位功能
    Get.snackbar('提示', '定位功能尚未實現');
  }

  // 搜尋地點（預留功能）
  void searchLocation(String query) {
    // TODO: 實作地點搜尋功能
    Get.snackbar('提示', '搜尋功能尚未實現');
  }
}
