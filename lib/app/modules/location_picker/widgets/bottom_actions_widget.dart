import 'package:flutter/material.dart';
import 'package:pocket_trac/colors.dart';

/// 底部操作按鈕組件
/// 可重複使用的底部操作區域，包含取消和確認按鈕
class BottomActionsWidget extends StatelessWidget {
  final VoidCallback onCancel;
  final VoidCallback onConfirm;
  final String cancelText;
  final String confirmText;
  final Widget? confirmButtonChild;

  const BottomActionsWidget({
    super.key,
    required this.onCancel,
    required this.onConfirm,
    this.cancelText = '取消',
    this.confirmText = '儲存',
    this.confirmButtonChild,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? ErpColors.darkCardBackground : Colors.white,
        border: Border(
          top: BorderSide(
            color: isDark ? ErpColors.darkBorder : ErpColors.border,
          ),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: getChildren(isDark).toList(growable: false),
        ),
      ),
    );
  }

  Iterable<Widget> getChildren(bool isDark) sync* {
    // 取消按钮 - 使用 OutlinedButton 風格
    yield Expanded(
      child: OutlinedButton(
        onPressed: onCancel,
        style: OutlinedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          side: BorderSide(
            color: isDark ? ErpColors.darkBorder : ErpColors.border,
          ),
        ),
        child: Text(
          cancelText,
          style: TextStyle(
            color:
                isDark ? ErpColors.darkTextSecondary : ErpColors.textSecondary,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
    yield const SizedBox(width: 12);
    // 確認按钮 - 支持自定義子組件
    yield Expanded(
      child: ElevatedButton(
        onPressed: onConfirm,
        style: ElevatedButton.styleFrom(
          backgroundColor: ErpColors.primary,
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 0,
        ),
        child: confirmButtonChild ??
            Text(
              confirmText,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
      ),
    );
  }
}
